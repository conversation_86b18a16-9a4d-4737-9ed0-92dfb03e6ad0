{"version": 2, "dgSpecHash": "nqSRiYEhIfw=", "success": false, "projectFilePath": "Q:\\test\\AspireApp2\\AspireApp2.ApiService\\AspireApp2.ApiService.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1008", "level": "Error", "message": "Projects that use central package version management should not define the version on the PackageReference items but on the PackageVersion items: Microsoft.AspNetCore.OpenApi.", "projectPath": "Q:\\test\\AspireApp2\\AspireApp2.ApiService\\AspireApp2.ApiService.csproj", "filePath": "Q:\\test\\AspireApp2\\AspireApp2.ApiService\\AspireApp2.ApiService.csproj", "targetGraphs": []}]}