{"version": 2, "dgSpecHash": "28DeMtdJvD4=", "success": false, "projectFilePath": "Q:\\test\\AspireApp2\\AspireApp2.ServiceDefaults\\AspireApp2.ServiceDefaults.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1008", "level": "Error", "message": "Projects that use central package version management should not define the version on the PackageReference items but on the PackageVersion items: Microsoft.Extensions.Http.Resilience;Microsoft.Extensions.ServiceDiscovery;OpenTelemetry.Exporter.OpenTelemetryProtocol;OpenTelemetry.Extensions.Hosting;OpenTelemetry.Instrumentation.AspNetCore;OpenTelemetry.Instrumentation.Http;OpenTelemetry.Instrumentation.Runtime.", "projectPath": "Q:\\test\\AspireApp2\\AspireApp2.ServiceDefaults\\AspireApp2.ServiceDefaults.csproj", "filePath": "Q:\\test\\AspireApp2\\AspireApp2.ServiceDefaults\\AspireApp2.ServiceDefaults.csproj", "targetGraphs": []}]}