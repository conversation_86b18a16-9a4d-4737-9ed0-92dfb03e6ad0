{"version": 3, "targets": {"net9.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net9.0": ["Aspire.Dashboard.Sdk.win-x64 >= 9.4.0", "Aspire.Hosting.AppHost >= 9.4.0", "Aspire.Hosting.Orchestration.win-x64 >= 9.4.0"]}, "packageFolders": {"q:\\.tools\\.nuget\\packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "Q:\\test\\AspireApp2\\AspireApp2.AppHost\\AspireApp2.AppHost.csproj", "projectName": "AspireApp2.AppHost", "projectPath": "Q:\\test\\AspireApp2\\AspireApp2.AppHost\\AspireApp2.AppHost.csproj", "packagesPath": "q:\\.tools\\.nuget\\packages", "outputPath": "Q:\\test\\AspireApp2\\AspireApp2.AppHost\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"noWarn": ["NU1507"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Aspire.Dashboard.Sdk.win-x64": {"target": "Package", "version": "[9.4.0, )", "autoReferenced": true}, "Aspire.Hosting.AppHost": {"target": "Package", "version": "[9.4.0, )"}, "Aspire.Hosting.Orchestration.win-x64": {"target": "Package", "version": "[9.4.0, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1008", "level": "Error", "message": "Projects that use central package version management should not define the version on the PackageReference items but on the PackageVersion items: Aspire.Hosting.AppHost."}]}