{"GlobalPropertiesHash": "Gm1UdSbk2zhh0XeKnNwtIrM0O9hZovRSiHxXod2fcTQ=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["79N/DaZb/Yks+B0zyUUeKCMD/iluMn6TRirBWPXhV3c=", "2ygYXWO6GqTvgUlxhocknOo5CxbDWSOLzACNFQixPMQ=", "0ntcVM1dS3O1QuUzTEci3l7/4cbDK9YfRpBOxOeBYII=", "ArseQWrL/BR5cpSOqMLfYMLpxsd+WWCCuKsxlJK/zzQ=", "DgBN9mOmxyk8V22BCClB2uKepyMIpuKZpoP9FLyIMME=", "gkNPViyP7ZC0c5bbaph8XzmNQdUsLWYaQ2DHean/PMY=", "3Bscyq9Zth8V5QHOweI1XdhUEhnnoZ3qay2l+km0ZsY=", "OBeiqAVTUQ1dOAQ/KA4YHclatbSyCs2dqlNaiP3N8lo=", "ThvzPIonTdkJe1hDp7FQQ1LtVrC0kSajPbVexUPfNn4=", "5Sxm1ugZRuZ/tL8y+AuNwJnpCSnk7wo2+EmUe+llgPE=", "SY3IUKuzdkae7qxvWSoeY9DA+atRmdRm/ujHbj8bVxk=", "v4S8KhF+ksXuEHajSpU8crds1KJGLgC8B9HVw0teLMA=", "EPNZhPBqezmfEcu1TsZGrxtGjR7mf8sL8iBMSHSJYQ8=", "gKEijXATAV1simRjEYsTU5SPir7khcQ+IH8jR4JkBF4=", "XXzDScDj41D3i/Jwv+KrBVM04t4KeVoWGinJl+0LMto=", "22sAS9TpVkfTSchSsbLKlmORG7p956Xu4LdbhCW0Iic=", "pYZdXEmB9K+YAnJ3uPfoMk1M0TXU1VltFIWMfKcEBQg=", "+3kvS86LXZFlIcEm9YSRky4ZMjQjaaUvl9BGKxuRYVQ=", "BomOzGaBolS5z09Squchw+WZBeoRTvH9KDLxfjWZW1k=", "5X/vtF4gasqbNF08xrrxHnI4cOEn7S9FHYMOyF4QAWo=", "GydrBsJFrUbNcx1QW4QKyTU02+Zhef0+1bwmwN199FQ=", "plrI4bEd3/4CPff1pFX/Wh77YzYQ+5rJrcypUPdiTnY=", "f63RSt56ChxPQR/PO56g+/522eg2I1n4AdGJlSK4esA=", "lgcp29lF0IRJrXAlvJT/X6UpwBMrwaO0a+zbLm++MBU=", "C8sCJEK/78EEehhNEbW6KYrIraMmJRG756TvaLBZaRs=", "ucnEFCW6WP4OdUdQX/bGxvWep73Rbemgcoi9pJN1Nyg=", "XtHmsswIG7vUsfMe1J9NcdxpzQTr9nS1LHfqZ1KbQ/k=", "3PrK6vKDLPDM0HyP05KXqjvqeHYT2tXj7B0Iqj5TFiw=", "UmWiSBY4RtMs75mnWKEXtDFlzQnckaNQgu0IQ6r3vhs=", "ar0w5pku/Zj+Y2p7ooP4PdCkOi+fzvDmBhC6PUwO7OM=", "+Du4fslKNwmMICd6DX4YJMhB+++mjE872kCnzDRey2U=", "YqWuUkkw8kNBGwaIsPeP0WXVRIkzJ4XTh93AudCSe/M=", "WfouZdEM9xs4VSvcOubq+40M/UXG+yP9ghcXxNSBsOg=", "VbUbrpK3iGYXdlnQAA/BpmA4rBIZZBhUy175MHBh9LI=", "wqAmuwlSTKw3EMYjhiAErFlkZBJ7LykliEua9I44msg=", "GiHd3is9MGJaelhs66LqzhWQlK/+glj8bTREYwxVDp4=", "XOYf4Vfgz+/jnFOgYEtnXzEopMDTyPFcbHQNBBt90DM=", "CMx/Qr0EcDkyU3feI4zUQZpdlk2/zV9qj68sqJ/gflQ=", "m8XO2DG0BzKo2OAYBRYHwACTG9JalQtWwLH2bvyuyTY=", "9kcCn/Aa4CjvMHEkhK2ntmsc7biBJCrpBjyZl2aBYdU=", "s366DoWK7xrPZnvZnbTF6Y7nLGWhbUIPKqWNvxa/1tU=", "Yojy90PwAJt8nZrshwf6Vl7IwBv8v3gF8CZ9mJvCac4=", "xHw9DlJXMZ9xFPFE3CPa+Bz3bPDI3NBdQbXXMkJO6Ko=", "y+jTex9dizPZCdA/oA6zNSymgmw8Q0JPKf5cujF0Os0=", "Fw4U2CuRwGPp34QuSq06iDxTCN2thOy/1PtSjbu0v8w=", "3mTwlrda8LvIJOW9RsO2xrKMFNzEIEM5dKdV3hK4+Gg=", "8TgRkbCtGZ0EFzuefAH0pe6T4rqyZDs68cShwVOJ3HA=", "H9/uY9a6SJoHlhrTsmtqEkbbUSPTmvx/sBwuW++llZk=", "cd9+YdtFNVdzqMJrDZVASaL/A8otFEMzSFb2T71VgHs=", "dvAaklIshVd7RUx/yyn2tR/VFjS8PeNq5lo4IoUL750=", "IJBDAxjeTvd8TDF0/e1QirWwAh6eEjXu/lFiEsGdBuE=", "lcB6jYoLNfrx/I4Y28Hkf1fM5sTtFNQgodGE0E30iqE=", "5k8ejthzfjf4QBBIuxkYFhmBFMzXzdV4Cq+bC7NXnpc=", "J0xxzWKnIk8Yu9elL16c42G1NuKuVyik6Hz3I89cDgw=", "BSh27XK1tDSAOXnJBuPEcx0Q4ji58bTMvbs6DOpWhBM=", "dWuLimk8GVA/BrS1vnIWe6iNCde/RovdfBE6v/pcHh4=", "HCwLmHdqi7uuGzXfO847xbFRoTsvu/RCmtLo0+tCsVI="], "CachedAssets": {"79N/DaZb/Yks+B0zyUUeKCMD/iluMn6TRirBWPXhV3c=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\app.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hqgu05fcj5", "Integrity": "OdSu6qanTBJEDiz55gimZND0r9vieZVWRP30yAK2C0M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2825, "LastWriteTime": "2025-08-12T02:06:16.7896831+00:00"}, "2ygYXWO6GqTvgUlxhocknOo5CxbDWSOLzACNFQixPMQ=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\favicon.png", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-08-12T02:06:16.7906834+00:00"}, "0ntcVM1dS3O1QuUzTEci3l7/4cbDK9YfRpBOxOeBYII=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t1cqhe9u97", "Integrity": "f4c8YV1qrr6RSiVYWMQT7R5W2OkT+iXmE5H69/uRvGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 74413, "LastWriteTime": "2025-08-12T02:06:16.7916828+00:00"}, "ArseQWrL/BR5cpSOqMLfYMLpxsd+WWCCuKsxlJK/zzQ=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-08-12T02:06:16.7956821+00:00"}, "DgBN9mOmxyk8V22BCClB2uKepyMIpuKZpoP9FLyIMME=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sejl45xvog", "Integrity": "hN2ttMG/K3wQlFrN3SbQ1YF4tIJHCE4oYP+QibT3vOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51800, "LastWriteTime": "2025-08-12T02:06:16.7966824+00:00"}, "gkNPViyP7ZC0c5bbaph8XzmNQdUsLWYaQ2DHean/PMY=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-08-12T02:06:16.8001878+00:00"}, "3Bscyq9Zth8V5QHOweI1XdhUEhnnoZ3qay2l+km0ZsY=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xvp3kq03qx", "Integrity": "M15fHTKfMK6cTars5c28PNhN6hD0qeUb3HPzOdoPwCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 74486, "LastWriteTime": "2025-08-12T02:06:16.8011905+00:00"}, "OBeiqAVTUQ1dOAQ/KA4YHclatbSyCs2dqlNaiP3N8lo=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-08-12T02:06:16.8057209+00:00"}, "ThvzPIonTdkJe1hDp7FQQ1LtVrC0kSajPbVexUPfNn4=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "22vffe00uq", "Integrity": "+rGG3u63SMkHL80Ga42LAawPUj7i8vaR2Kuenqlta2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51875, "LastWriteTime": "2025-08-12T02:06:16.8067206+00:00"}, "5Sxm1ugZRuZ/tL8y+AuNwJnpCSnk7wo2+EmUe+llgPE=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-08-12T02:06:16.8097198+00:00"}, "SY3IUKuzdkae7qxvWSoeY9DA+atRmdRm/ujHbj8bVxk=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qesaa3a1fm", "Integrity": "rjusxCRzKjztCcfgOVduAc3e3Z7KvimJGokJSeADNmE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12661, "LastWriteTime": "2025-08-12T02:06:16.8675591+00:00"}, "v4S8KhF+ksXuEHajSpU8crds1KJGLgC8B9HVw0teLMA=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-08-12T02:06:16.8705575+00:00"}, "EPNZhPBqezmfEcu1TsZGrxtGjR7mf8sL8iBMSHSJYQ8=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tmc1g35s3z", "Integrity": "y72bT5TKmFmfvw3bNEzPRNZa/8595xtseZIcBUV0PdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10131, "LastWriteTime": "2025-08-12T02:06:16.8715586+00:00"}, "gKEijXATAV1simRjEYsTU5SPir7khcQ+IH8jR4JkBF4=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-08-12T02:06:16.8725583+00:00"}, "XXzDScDj41D3i/Jwv+KrBVM04t4KeVoWGinJl+0LMto=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rxsg74s51o", "Integrity": "gRKIHVkBgnCPUs/hmZsXynZ9ZJpvsE77idtljDkrWGI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12651, "LastWriteTime": "2025-08-12T02:06:16.8735576+00:00"}, "22sAS9TpVkfTSchSsbLKlmORG7p956Xu4LdbhCW0Iic=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-08-12T02:06:16.8765575+00:00"}, "pYZdXEmB9K+YAnJ3uPfoMk1M0TXU1VltFIWMfKcEBQg=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q9ht133ko3", "Integrity": "UkajePbMzGj8y73Imkd3dWWnLJr0v2CrBCLMrVMleP8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10203, "LastWriteTime": "2025-08-12T02:06:16.8775578+00:00"}, "+3kvS86LXZFlIcEm9YSRky4ZMjQjaaUvl9BGKxuRYVQ=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-08-12T02:06:16.8795578+00:00"}, "BomOzGaBolS5z09Squchw+WZBeoRTvH9KDLxfjWZW1k=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gye83jo8yx", "Integrity": "uIzOR4gILoNjCGNryIsWnkEGO3BSwRa/MLjr73tDodE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 113224, "LastWriteTime": "2025-08-12T02:06:16.8815579+00:00"}, "5X/vtF4gasqbNF08xrrxHnI4cOEn7S9FHYMOyF4QAWo=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-08-12T02:06:16.8875577+00:00"}, "GydrBsJFrUbNcx1QW4QKyTU02+Zhef0+1bwmwN199FQ=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wl58j5mj3v", "Integrity": "ZBXbYF8OZrF1GDOLMLvW/zRPtx1RfeMDYj5D2Wb/9jo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85357, "LastWriteTime": "2025-08-12T02:06:16.8895584+00:00"}, "plrI4bEd3/4CPff1pFX/Wh77YzYQ+5rJrcypUPdiTnY=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-08-12T02:06:16.8935579+00:00"}, "f63RSt56ChxPQR/PO56g+/522eg2I1n4AdGJlSK4esA=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d4r6k3f320", "Integrity": "MwFckg8YbY2WrveNQnXkfULkprYhFOq+t1BH92A7GsI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 113083, "LastWriteTime": "2025-08-12T02:06:16.8955582+00:00"}, "lgcp29lF0IRJrXAlvJT/X6UpwBMrwaO0a+zbLm++MBU=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-08-12T02:06:16.9005579+00:00"}, "C8sCJEK/78EEehhNEbW6KYrIraMmJRG756TvaLBZaRs=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "keugtjm085", "Integrity": "7MYEMUFgHEc1CjrfAK/TlV30POxgoxXk2b+vAs/143U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85286, "LastWriteTime": "2025-08-12T02:06:16.9025581+00:00"}, "ucnEFCW6WP4OdUdQX/bGxvWep73Rbemgcoi9pJN1Nyg=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-08-12T02:06:16.9065609+00:00"}, "XtHmsswIG7vUsfMe1J9NcdxpzQTr9nS1LHfqZ1KbQ/k=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zub09dkrxp", "Integrity": "CzmECrtXV8BSk/+8SG+Rw7Fr+3hCt2bS2bEkS4YD3PQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 293102, "LastWriteTime": "2025-08-12T02:06:16.9115603+00:00"}, "3PrK6vKDLPDM0HyP05KXqjvqeHYT2tXj7B0Iqj5TFiw=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-08-12T02:06:16.9245605+00:00"}, "UmWiSBY4RtMs75mnWKEXtDFlzQnckaNQgu0IQ6r3vhs=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "43atpzeawx", "Integrity": "sKIQRfQriISuQ9l/44b1zHfQniGXJhGonVtB2LlSuIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232808, "LastWriteTime": "2025-08-12T02:06:16.9295723+00:00"}, "ar0w5pku/Zj+Y2p7ooP4PdCkOi+fzvDmBhC6PUwO7OM=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-08-12T02:06:16.9415759+00:00"}, "+Du4fslKNwmMICd6DX4YJMhB+++mjE872kCnzDRey2U=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ynyaa8k90p", "Integrity": "/rceNUMI0RlFJugRzjua815bgXwawEJBGBPh4Vx3r38=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 292288, "LastWriteTime": "2025-08-12T02:06:16.9473176+00:00"}, "YqWuUkkw8kNBGwaIsPeP0WXVRIkzJ4XTh93AudCSe/M=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-08-12T02:06:16.9611365+00:00"}, "WfouZdEM9xs4VSvcOubq+40M/UXG+yP9ghcXxNSBsOg=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c63t5i9ira", "Integrity": "rI1w2GDxZPCr77bpZIGQ8mUvMVtEr+gBtahl5RIPJpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232916, "LastWriteTime": "2025-08-12T02:06:16.9741985+00:00"}, "VbUbrpK3iGYXdlnQAA/BpmA4rBIZZBhUy175MHBh9LI=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-08-12T02:06:17.038278+00:00"}, "wqAmuwlSTKw3EMYjhiAErFlkZBJ7LykliEua9I44msg=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4c0non2i4e", "Integrity": "jCoJWEsEYLCBez8FRZCd6CT/Zxybr1L34xIJ+P534Mo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 232139, "LastWriteTime": "2025-08-12T02:06:17.0442805+00:00"}, "GiHd3is9MGJaelhs66LqzhWQlK/+glj8bTREYwxVDp4=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9pm1no9j49", "Integrity": "LVDcCdxF0O1Y16LTaTFPXMX/oDg0fu16G9jrb0h+onM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-08-12T02:06:17.0542808+00:00"}, "XOYf4Vfgz+/jnFOgYEtnXzEopMDTyPFcbHQNBBt90DM=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jx9dt1atkh", "Integrity": "ecsItXqJb7LZrR3ml3h1SeXSnMyBA9sCAjZH2k64rS0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 98729, "LastWriteTime": "2025-08-12T02:06:17.0577886+00:00"}, "CMx/Qr0EcDkyU3feI4zUQZpdlk2/zV9qj68sqJ/gflQ=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kj1yp581dm", "Integrity": "sBY2KCy7/UmAlj3AWcVFMIbzsF+iDxDHPx31ggV8MMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-08-12T02:06:17.0657885+00:00"}, "m8XO2DG0BzKo2OAYBRYHwACTG9JalQtWwLH2bvyuyTY=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m4pd7hf0rx", "Integrity": "dvuindR/7K9Ux6BDXH+kX27zW/KjGgOt28XYziSRbKs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 158234, "LastWriteTime": "2025-08-12T02:06:17.0697885+00:00"}, "9kcCn/Aa4CjvMHEkhK2ntmsc7biBJCrpBjyZl2aBYdU=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "oiw5rtlwyt", "Integrity": "IO2I7g40FgO5GT9757Uu8vF/D1AEOHRk8h/cBurCFgs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-08-12T02:06:17.0767884+00:00"}, "s366DoWK7xrPZnvZnbTF6Y7nLGWhbUIPKqWNvxa/1tU=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9er8ybo8ma", "Integrity": "sSo8MU5sMqlHRquAekxYt3IzE3LF9V33klJwb6Aeg+Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 91943, "LastWriteTime": "2025-08-12T02:06:17.0797884+00:00"}, "Yojy90PwAJt8nZrshwf6Vl7IwBv8v3gF8CZ9mJvCac4=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "02gsvt8l2e", "Integrity": "uOyOrUyz73HkobcgYOOEmT4Fn86aAtNmwod7j86j80w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-08-12T02:06:17.0847886+00:00"}, "xHw9DlJXMZ9xFPFE3CPa+Bz3bPDI3NBdQbXXMkJO6Ko=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sxcfb16nca", "Integrity": "wGFaDQK/tUSl+nESKrpK2j1oRYgWolLABNX1n1aq+70=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 167901, "LastWriteTime": "2025-08-12T02:06:17.0897906+00:00"}, "y+jTex9dizPZCdA/oA6zNSymgmw8Q0JPKf5cujF0Os0=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8y4vlu10z", "Integrity": "VJlrMD2hcd4uKsPVWB9Fw1d11UDkzgfeEGdO1ntXLDY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-08-12T02:06:17.0967885+00:00"}, "Fw4U2CuRwGPp34QuSq06iDxTCN2thOy/1PtSjbu0v8w=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nnhcgsbcv1", "Integrity": "Y5RQr85i53g5LWwja4KEO0XzxEpalfWgvkzfKEabae4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 78631, "LastWriteTime": "2025-08-12T02:06:17.0997888+00:00"}, "3mTwlrda8LvIJOW9RsO2xrKMFNzEIEM5dKdV3hK4+Gg=": {"Identity": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "AspireApp2.Web", "SourceType": "Discovered", "ContentRoot": "Q:\\test\\AspireApp2\\AspireApp2.Web\\wwwroot\\", "BasePath": "_content/AspireApp2.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y0olr4q2ur", "Integrity": "4hdcxNAp0i4AC9HclEgoUru4XwAvwd90HYdCQ17wkRY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-08-12T02:06:17.1057881+00:00"}}, "CachedCopyCandidates": {}}